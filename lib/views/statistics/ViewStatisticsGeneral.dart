import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/statistics/EventStatistics.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/enum_stats_format.dart';
import 'package:theradom/models/stat_field_data_group.dart';
import 'package:theradom/utils/theme/app_theme.dart';

import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/statistics/ViewMultipleData.dart';
import 'package:theradom/views/statistics/ViewSimpleData.dart';
import 'package:theradom/blocs/statistics/BlocStatistics.dart';
import 'package:theradom/blocs/statistics/StateStatistics.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/views/statistics/view_charts_stats.dart';
import 'package:theradom/widgets/DateRangeAction.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/background_header.dart';
import 'package:theradom/widgets/custom_app_bar.dart';
import 'package:theradom/widgets/buttons/fab.dart';
import 'package:theradom/widgets/tab_item.dart';

class ViewStatisticsGeneral extends StatefulWidget {
  final BlocStatistics blocStatistics;

  ViewStatisticsGeneral({required this.blocStatistics});

  @override
  _ViewStatisticsGeneralState createState() => _ViewStatisticsGeneralState();
}

class _ViewStatisticsGeneralState extends State<ViewStatisticsGeneral>
    with SingleTickerProviderStateMixin {
  late final BlocStatistics _blocStatistics;

  int _selectedMaxCount = Config.statsMaxLines;
  List<StatsFieldDataGroup> _simpleDataFieldGroupList = [];
  List<StatsFieldDataGroup> _multipleDataFieldGroupList = [];
  StatsFormat _format = StatsFormat.details;

  late DateTime _dateTimeTo;
  late DateTime _dateTimeFrom;

  late final TabController _tabController;
  AutoSizeGroup _myGroup = AutoSizeGroup();

  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
    _blocStatistics = widget.blocStatistics;
    _dateTimeTo = DateTime.now().subtract(Duration(days: 1));
    _dateTimeFrom =
        DateTime(_dateTimeTo.year, _dateTimeTo.month - 1, _dateTimeTo.day);
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    super.dispose();
    Config.blockBackBtnPop = true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(backBtnEnabled: true),
      body: DefaultTabController(
        length: 2,
        initialIndex: 0,
        child: Background(
          child: Scaffold(
            backgroundColor: Colors.transparent,
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(190),
              child: Column(
                children: [
                  BackgroundHeader(
                    label: allTranslations.text('stats'),
                  ),
                  Container(
                      padding: EdgeInsets.all(10.0),
                      margin: EdgeInsets.symmetric(horizontal: 5.0),
                      child: TabBar(
                        controller: _tabController,
                        indicator: BoxDecoration(
                            borderRadius: BorderRadius.circular(25.0),
                            color: AppTheme.secondaryColor),
                        labelColor: AppTheme.primaryColor,
                        unselectedLabelColor: AppTheme.backgroundColor,
                        indicatorSize: TabBarIndicatorSize.tab,
                        tabs: [
                          TabItem(
                            title: allTranslations.text('simple_data'),
                            group: _myGroup,
                          ),
                          TabItem(
                              title: allTranslations.text('multiple_data'),
                              group: _myGroup)
                        ],
                      )),
                  Container(
                    height: 50,
                    child: DateRangeAction(
                        startDate: _dateTimeFrom,
                        endDate: _dateTimeTo,
                        setDates: (startDate, endDate) {
                          setState(() {
                            _dateTimeFrom = startDate;
                            _dateTimeTo = endDate;
                          });
                        },
                        onAction: () => null),
                  )
                ],
              ),
            ),
            body: BlocListener<BlocStatistics, StateStatistics>(
                bloc: _blocStatistics,
                listener: (context, state) {
                  print("🔍 LISTENER DEBUG: État reçu: ${state.runtimeType}");

                  if (state is StateStatisticsDisplay) {
                    print(
                        "🔍 LISTENER DEBUG: StateStatisticsDisplay - reloadChart: ${state.reloadChart}, erreur: ${state.errorDetails?.message}");
                    if (state.errorDetails == null) {
                      if (!state.reloadChart) {
                        _pushToChartScreen(state);
                      }
                    } else {
                      AlertBox(
                              context: context,
                              title: allTranslations.text('error'),
                              description: state.errorDetails!.message)
                          .showFlushbar(warning: true);
                    }
                  }

                  if (state is StateStatisticsLoadingCharts) {
                    print(
                        "🔍 LISTENER DEBUG: StateStatisticsLoadingCharts - loading: ${state.loading}, reloadChart: ${state.reloadChart}");

                    if (!state.reloadChart) {
                      if (state.loading) {
                        print("🔍 LISTENER DEBUG: Ouverture popup");
                        AlertBox(
                                context: context,
                                title: allTranslations.text('please_wait'))
                            .showLoading();
                      } else {
                        print("🔍 LISTENER DEBUG: Tentative fermeture popup");
                        try {
                          Navigator.of(context).pop();
                          print("🔍 LISTENER DEBUG: Popup fermée avec succès");
                        } catch (e) {
                          print(
                              "🔍 LISTENER DEBUG: Erreur fermeture popup: $e");
                        }
                      }
                    } else {
                      print(
                          "🔍 LISTENER DEBUG: Popup ignorée car reloadChart = true");
                    }
                  }
                },
                child: BlocBuilder<BlocStatistics, StateStatistics>(
                  bloc: _blocStatistics,
                  buildWhen: (previousState, state) {
                    bool rebuild;
                    if ((state is StateStatisticsDisplay) ||
                        (state is StateStatisticsLoadingCharts)) {
                      rebuild = false;
                    } else {
                      rebuild = true;
                    }

                    return rebuild;
                  },
                  builder: (context, state) {
                    Widget screen = Container();

                    if (state is StateStatisticsUninitialized) {
                      return PlatformCircularIndicator(darkIndicator: false);
                    }

                    if (state is StateStatisticsMainScreen) {
                      ErrorDetails? errorDetailsPreviousDataResponse =
                          state.errorDetailsPreviousDataResponse;

                      if (errorDetailsPreviousDataResponse == null) {
                        if (_simpleDataFieldGroupList.length == 0) {
                          _simpleDataFieldGroupList = state.simpleDataGroupList;
                          _simpleDataFieldGroupList.forEach((element) {
                            element.deselectAll();
                          });
                        }
                        if (_multipleDataFieldGroupList.length == 0) {
                          _multipleDataFieldGroupList =
                              state.multipleDataGroupList;
                          _multipleDataFieldGroupList.forEach((element) {
                            element.deselectAll();
                          });
                        }
                        screen = _buildTabBarView();
                      } else {
                        screen = _buildErrorScreen(
                            errorDetailsPreviousDataResponse.message);
                      }
                    }

                    return screen;
                  },
                )),
            floatingActionButton: Fab(
                svgPicture: SvgPicture.asset("assets/images/icons/icStat.svg"),
                onPressed: _createAndLaunchEventToDisplayData),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: <Widget>[
        ViewSimpleData(
            dataFieldGroupList: _simpleDataFieldGroupList,
            onChange: _updateSimpleDataGroupList),
        ViewMultipleData(
            dataFieldGroupList: _multipleDataFieldGroupList,
            onFormatChanged: _updateFormat,
            onChange: _updateMultipleDataGroupList)
      ],
    );
  }

  Widget _buildErrorScreen(String message) {
    return Center(
        child: Text(
      message,
      maxLines: 1,
      style: TextStyle(
          fontSize: FontSizeController.of(context).fontSize,
          color: AppTheme.backgroundColor),
    ));
  }

  void _updateSimpleDataGroupList(
      List<StatsFieldDataGroup> updatedSimpleDataGroup) {
    _simpleDataFieldGroupList = updatedSimpleDataGroup;
  }

  void _updateMultipleDataGroupList(
      List<StatsFieldDataGroup> updatedMultipleDataGroup) {
    _multipleDataFieldGroupList = updatedMultipleDataGroup;
  }

  void _updateFormat(StatsFormat format) {
    _format = format;
  }

  void _pushToChartScreen(StateStatisticsDisplay state) async {
    // push vers ViewStatsCharts
    await Navigator.push(
        context,
        CupertinoPageRoute(
            builder: (context) => ViewChartsStats(
                normaliteList: [],
                iLinedataSetList: [],
                blocStatistics: _blocStatistics,
                dateFrom: _dateTimeFrom,
                dateTo: _dateTimeTo,
                onDataReloadFrom: _reloadChartFrom)));
  }

  void _reloadChartFrom(DateTime from, DateTime to) {
    String type = _tabController.index == 0 ? "simple" : "table";
    _launchEventStatisticsDisplay(type, from, to, true);
  }

  void _createAndLaunchEventToDisplayData() {
    if (_tabController.index == 0) {
      if ((_simpleSelectedItemsCount() > 0) &&
          (_simpleSelectedItemsCount() <= _selectedMaxCount)) {
        _launchEventStatisticsDisplay(
            "simple", _dateTimeFrom, _dateTimeTo, false);
      }
    } else if (_tabController.index == 1) {
      if ((_multipleSelectedItemsCount() > 0) &&
          (_multipleSelectedItemsCount() <= _selectedMaxCount)) {
        _launchEventStatisticsDisplay(
            "table", _dateTimeFrom, _dateTimeTo, false);
      }
    }
  }

  int _simpleSelectedItemsCount() {
    int count = 0;
    _simpleDataFieldGroupList.forEach((group) {
      count = count + group.selectedFieldCount();
    });
    return count;
  }

  int _multipleSelectedItemsCount() {
    int count = 0;
    _multipleDataFieldGroupList.forEach((group) {
      count = count + group.selectedFieldCount();
    });
    return count;
  }

  void _launchEventStatisticsDisplay(
      String type, DateTime dateFrom, DateTime dateTo, bool reloadChart) {
    // lance l'event pour ouvrir l'écran de visualisation des stats
    _blocStatistics.add(EventStatisticsDisplay(
        simpleDataGroupList: _simpleDataFieldGroupList,
        multipleDataGroupList: _multipleDataFieldGroupList,
        startDate: dateFrom,
        endDate: dateTo,
        format: _format.toString(),
        type: type,
        reloadChart: reloadChart));
  }
}
