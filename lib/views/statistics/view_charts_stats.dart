import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/statistics/BlocStatistics.dart';
import 'package:theradom/blocs/statistics/StateStatistics.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/change_orientation.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/multiline_chart.dart';
import 'package:theradom/widgets/stats/stats_shortcut_button_row.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/config.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:fl_chart/fl_chart.dart';

/// Vue d'un graphique, affiche aussi bien les données depuis "Statistique"

class ViewChartsStats extends StatefulWidget {
  final DateTime dateFrom;
  final DateTime dateTo;
  final List<dynamic> iLinedataSetList;
  final BlocStatistics blocStatistics;
  final List<double> normaliteList;
  final void Function(DateTime, DateTime) onDataReloadFrom;

  ViewChartsStats({
    required this.dateFrom,
    required this.dateTo,
    required this.iLinedataSetList,
    required this.blocStatistics,
    required this.normaliteList,
    required this.onDataReloadFrom,
  });

  @override
  _ViewChartsStatsState createState() => _ViewChartsStatsState();
}

class _ViewChartsStatsState extends State<ViewChartsStats> {
  late DateTime _dateFrom;
  late DateTime _dateTo;
  double _fontSize = 18.0;

  @override
  void initState() {
    super.initState();
    OrientationSetter.change.toLandscape();
    _dateFrom = widget.dateFrom;
    _dateTo = widget.dateTo;
    Config.blockBackBtnPop = false;
  }

  @override
  void dispose() {
    OrientationSetter.change.toPortrait();
    Config.blockBackBtnPop = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        OrientationSetter.change.toPortrait();
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          toolbarHeight: 0,
          backgroundColor: AppTheme.primaryColor,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        body: Container(
          color: AppTheme.primaryColor,
          padding: EdgeInsets.all(10.0),
          child: Column(
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildDateRangeWidget(),
                  SizedBox(width: 30.0),
                  StatsShortcutButtonsRow(
                    onDatesSelected: _setDatesAndReloadFromDate,
                  ),
                  SizedBox(width: 30.0),
                  SvgButton(
                    asset: "assets/images/icons/icReduceScreen.svg",
                    assetColor: AppTheme.backgroundColor,
                    onTap: () {
                      OrientationSetter.change.toPortrait();
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
              SizedBox(height: 10.0),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: BlocBuilder<BlocStatistics, StateStatistics>(
                    bloc: widget.blocStatistics,
                    buildWhen: (previousState, state) {
                      bool rebuild;
                      if (state is StateStatisticsMainScreen) {
                        rebuild = false;
                      } else {
                        rebuild = true;
                      }
                      return rebuild;
                    },
                    builder: (context, state) {
                      Widget screen = Container();

                      if (state is StateStatisticsLoadingCharts) {
                        if (state.reloadChart) {
                          screen =
                              PlatformCircularIndicator(darkIndicator: true);
                        }
                      }

                      if (state is StateStatisticsDisplay) {
                        print(
                            "🔍 STATS UI DEBUG: StateStatisticsDisplay reçu - ${state.iLineDataSetList.length} séries, erreur: ${state.errorDetails?.message}");
                        if (state.errorDetails == null &&
                            state.iLineDataSetList.isNotEmpty) {
                          screen = MultilineChart(
                            iLineDataSetList: state.iLineDataSetList,
                            limitLineList2: const [],
                            enableGestures: true,
                          );
                        } else {
                          screen = Center(
                            child: Text(
                              state.errorDetails?.message ??
                                  allTranslations.text("no_data"),
                              style: TextStyle(
                                fontSize:
                                    FontSizeController.of(context).fontSize,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          );
                        }
                      }

                      return screen;
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeWidget() {
    EdgeInsets padding = EdgeInsets.only(
      top: 4.0,
      bottom: 3.0,
      left: 15.0,
      right: 15.0,
    );

    BoxDecoration boxDecoration = BoxDecoration(
      color: AppTheme.backgroundColor,
      borderRadius: BorderRadius.all(Radius.circular(18.0)),
    );

    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Date début
          Container(
            padding: padding,
            decoration: boxDecoration,
            child: Text(
              _dateFrom.toLanguageStringShort(),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
                fontSize: _fontSize,
              ),
            ),
          ),
          SizedBox(width: 10.0),
          SvgPicture.asset("assets/images/icons/icToTime.svg"),
          SizedBox(width: 10.0),
          // Date fin
          Container(
            padding: padding,
            decoration: boxDecoration,
            child: Text(
              _dateTo.toLanguageStringShort(),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
                fontSize: _fontSize,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _setDatesAndReloadFromDate(DateTime from, DateTime to) {
    _setDates(from, to);
    widget.onDataReloadFrom(from, to);
  }

  void _setDates(DateTime from, DateTime to) {
    setState(() {
      _dateFrom = from;
      _dateTo = to;
    });
  }
}
