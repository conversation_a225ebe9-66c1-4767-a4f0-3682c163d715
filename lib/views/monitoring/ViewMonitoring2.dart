import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:theradom/blocs/documents/BlocDocuments.dart';
import 'package:theradom/blocs/monitoring/BlocMonitoring1.dart';
import 'package:theradom/blocs/monitoring/StateMonitoring1.dart';
import 'package:theradom/blocs/monitoring_details/monitoring_details_bloc_provider.dart';
import 'package:theradom/blocs/prescription/BlocPrescription.dart';
import 'package:theradom/blocs/statistics/BlocStatistics.dart';
import 'package:theradom/blocs/statistics/EventStatistics.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/utils/UrlLauncherUtils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/documents/view_documents_general.dart';
import 'package:theradom/widgets/buttons/monitoring_card_button.dart';
import 'package:theradom/views/monitoring/view_monitoring_details_general.dart';
import 'package:theradom/views/prescription/view_prescription_general.dart';
import 'package:theradom/views/statistics/ViewStatisticsGeneral.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/blocs/monitoring/EventMonitoring1.dart';
import 'package:theradom/widgets/icons/documents_icon.dart';
import 'package:theradom/config.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/widgets/monitoring/session_cards_stack.dart';
import 'package:theradom/widgets/monitoring/session_date_selection.dart';

/// Vue principale de la partie "Mes suivis"

class ViewMonitoring2 extends StatefulWidget {
  final String monitoringType;
  final List<SeancePrecedente> sessionList;
  final BlocMonitoring1 blocMonitoring1;

  ViewMonitoring2(
      {required this.monitoringType,
      required this.sessionList,
      required this.blocMonitoring1});

  @override
  _ViewMonitoring2State createState() => _ViewMonitoring2State();
}

class _ViewMonitoring2State extends State<ViewMonitoring2> {
  late final String _suivi;
  late final BlocMonitoring1 _blocMonitoring1;
  late final SeancePrecedente? _lastSession;
  late final List<SeancePrecedente> _previousSessions;
  List<String> _sessionToEndDateList = [];
  List<String> _sessionToEndIdList = [];
  late final BlocDocuments _blocDocuments;
  late final BlocStatistics _blocStatistics;
  late final BlocPrescription _blocPrescription;
  late bool _isSession;

  @override
  void initState() {
    super.initState();
    _suivi = widget.monitoringType;
    _isSession = MonitoringUtils.isMonitoringSession(_suivi);
    _previousSessions = widget.sessionList;

    Config.notificationResponse!.seanceNonFinie
        .forEach((id, unfinishedSession) {
      if (unfinishedSession.numTypeSuivi == _suivi) {
        _sessionToEndDateList.add(unfinishedSession.dateSeanceNonFinie);
        _sessionToEndIdList.add(id);
      }
    });

    if (_previousSessions.length > 0) {
      // si au moins une séance existe
      _lastSession = _pickLastSession();
/*       _previousSessions.removeWhere(
          (element) => element.numSeance == _lastSession!.numSeance); */
    }
    _blocMonitoring1 = widget.blocMonitoring1;
    _blocDocuments = BlocProvider.of<BlocDocuments>(context);
    _blocStatistics = BlocProvider.of<BlocStatistics>(context);
    _blocPrescription = BlocProvider.of<BlocPrescription>(context);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener(
      bloc: _blocMonitoring1,
      listener: (context, state) {
        if (state is StateMonitoringNewSession) {
          // séance créée avec succès
          if (state.error == null) {
            _pushToSessionScreen(state.seance!, false);
          }
        }
      },
      child: SafeArea(
          bottom: false,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(
                    left: 20.0,
                    right: 20.0,
                    top: Config.basicUserInfos!.monitoringCount > 1
                        ? 10.0
                        : 20.0,
                    bottom: 5.0),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(30.0)),
                ),
                // stack d'infos sur les séances du suivi
                child: SessionCardsStack(
                    sessions: widget.sessionList,
                    sessionToEndIds: _sessionToEndDateList,
                    isSession: _isSession,
                    onSessionSelected: _pushToSessionScreen,
                    onSessionCreationSelected: () => _showAlertNewSession(0)),
              ),
              Expanded(
                  child: Container(
                decoration: BoxDecoration(
                    border: Border(
                        top: BorderSide(
                            color: AppTheme.primaryColorDarker, width: 2)),
                    boxShadow: [
                      BoxShadow(
                          color: AppTheme.primaryColor.withOpacity(0.3),
                          spreadRadius: 0.0,
                          blurRadius: 5.0,
                          offset: Offset(0.0, 5.0))
                    ]),
                child: GridView.count(
                    primary: false,
                    padding: const EdgeInsets.only(
                        left: 20.0, right: 20.0, top: 14.0, bottom: 120.0),
                    crossAxisSpacing: 20,
                    mainAxisSpacing: 20,
                    childAspectRatio: 0.75,
                    crossAxisCount:
                        MediaQuery.of(context).size.shortestSide < 600 ? 2 : 3,
                    children: _getGridViewChildren()),
              )),
            ],
          )),
    );
  }

  List<Widget> _getGridViewChildren() {
    List<Widget> children = [];
    AutoSizeGroup autoSizeGroup = AutoSizeGroup();

    if (Config.notificationResponse!.teleconsultation != "") {
      children.add(MonitoringCardButton(
          title: allTranslations.text('teleconsultation'),
          iconWidget: Icon(FontAwesomeIcons.video,
              size: 32, color: AppTheme.tertiaryColor),
          group: autoSizeGroup,
          onTap: () => UrlLauncherUtils.launchURL(
              Config.notificationResponse!.teleconsultation),
          spinIcon: true));
    }

    children.addAll([
      /*
          MonitoringCardButton(
              title: "Fin le 23/05/2023",
              iconWidget: SvgPicture.asset(
                "assets/images/icons/icSuiviBis.svg",
                height: 36,
                width: 36,
              ),
              group: autoSizeGroup,
              onTap: () => null
          ),
          */
      MonitoringCardButton(
          title: allTranslations.text('prescription'),
          iconWidget: SvgPicture.asset("assets/images/icons/icPrescrip.svg"),
          group: autoSizeGroup,
          onTap: () => _pushToPrescriptionScreen(context)),
      MonitoringCardButton(
        title: allTranslations.text('docs'),
        iconWidget: DocumentsIcon(),
        group: autoSizeGroup,
        onTap: _pushToDocsScreen,
      ),
      MonitoringCardButton(
          title: allTranslations.text('stats'),
          iconWidget: SvgPicture.asset("assets/images/icons/icStat.svg"),
          group: autoSizeGroup,
          onTap: _pushToStatsScreen)
    ]);

    return children;
  }

  void _pushToPrescriptionScreen(BuildContext context) {
    // montre la prescription
    Navigator.push(
      context,
      CupertinoPageRoute(
          builder: (context) => ViewPrescriptionGeneral(
              blocPrescription: _blocPrescription, suivi: _suivi)),
    );
  }

  void _pushToDocsScreen() {
    // push vers l'écran des documents
    Navigator.push(
        context,
        CupertinoPageRoute(
            builder: (context) =>
                ViewDocumentsGeneral(blocDocuments: _blocDocuments)));
  }

  void _pushToStatsScreen() {
    // push vers l'écran des statistiques
    _launchEventStatisticsMainScreen();
    Navigator.push(
        context,
        CupertinoPageRoute(
            builder: (context) =>
                ViewStatisticsGeneral(blocStatistics: _blocStatistics)));
  }

  void _pushToSessionScreen(
      SeancePrecedente seancePrecedente, bool terminee) async {
    // push vers le détails de la séance
    await Navigator.push(
      context,
      CupertinoPageRoute(
          builder: (context) => BlocProvider.value(
                value: _blocMonitoring1,
                child: MonitoringDetailsBlocProvider(
                  child: ViewMonitoringDetailsGeneral(
                      suivi: _suivi,
                      onPrescriptionButtonTapped: () =>
                          _pushToPrescriptionScreen(context)),
                  suivi: _suivi,
                  estTerminee: terminee,
                  seancePrecedente: seancePrecedente,
                ),
              )),
    );
    _launchMonitoringMainScreenEvent();
  }

  Future<bool> _checkSelectedDateAndBeforeSessionCreation(DateTime date) async {
    // check que la date choisie est bien un jour de dialyse pour le patient
    bool? create = true;
    int weekday = date.weekday;

    if (!Config.basicUserInfos!.joursDial.canCreateSessionOnWeekday(weekday)) {
      create = await AlertBox(
              context: context,
              title: allTranslations.text('session_from') +
                  " ${date.toLanguageString()}",
              description: allTranslations.text('no_dialysis_at_this_date_msg'),
              confirmBtnLabel: allTranslations.text('continue'))
          .showConfirmation();
      if (create == null) {
        create = false;
      }
    }
    return create;
  }

  void _showAlertNewSession(int i) async {
    // montre une boite de dialogue pour la création d'une nouvelle séance
    // 0 -> nouvelle séance
    // 1 -> nouvelle séance passée

    bool seanceEnCours = false;
    late SeancePrecedente seancePrecedenteEnCours;
    // check si une seance est déjà en cours

    widget.sessionList.forEach((seancePrecedente) {
      _sessionToEndIdList.forEach((seanceATerminerId) {
        if (seanceATerminerId == seancePrecedente.numSeance) {
          seancePrecedenteEnCours = seancePrecedente;
          seanceEnCours = true;
        }
        if (_lastSession!.numSeance == seanceATerminerId) {
          seancePrecedenteEnCours = _lastSession!;
          seanceEnCours = true;
        }
      });
    });

    //List repList = await Dialogs.showSessionCreation(context, seanceEnCours, _isSession);

    // affiche le widget de création d'une séance
    DateTime now = DateTime.now();
    DateTime selectedDateTime = now;
    int? choix = await showDialog(
        context: context,
        barrierColor: Colors.transparent,
        builder: (_) {
          return SessionDateSelection(
            estSeance: _isSession,
            minDateTime: now.subtract(Duration(days: 10)),
            maxDateTime: now,
            initialDateTime: now,
            seanceEnCours: seanceEnCours,
            onDaySelected: (date) {
              selectedDateTime = date;
            },
          );
        });

    switch (choix) {
      case 0:
        {
          // lancer la séance
          _pushToSessionScreen(seancePrecedenteEnCours, false);
        }
        break;
      case 1:
        {
          // créer la séance
          bool createSession = _isSession
              ? await _checkSelectedDateAndBeforeSessionCreation(
                  selectedDateTime)
              : true;
          if (createSession) _launchCreateSessionEvent(selectedDateTime);
        }
        break;
      default:
        {}
    }
  }

  SeancePrecedente _pickLastSession() {
    SeancePrecedente seancePrecedente = widget.sessionList.first;
    List<SeancePrecedente> seanceMemeDate = [];
    seanceMemeDate = widget.sessionList
        .where((seance) => seance.dateSeance == seancePrecedente.dateSeance)
        .toList();
    // estTermine !_sessionToEndIdList.contains(_previousSessions[index].numSeance)
    seanceMemeDate.forEach((seance) {
      if (_sessionToEndIdList.contains(seance.numSeance)) {
        seancePrecedente = seance;
      }
    });
    return seancePrecedente;
  }

  void _launchCreateSessionEvent(DateTime date) {
    // lance l'event de création d'une séance
    _blocMonitoring1.add(EventMonitoringNewSession(suivi: _suivi, date: date));
  }

  void _launchMonitoringMainScreenEvent() {
    // lance l'event d'accès à l'écran principal du suivi
    _blocMonitoring1.add(EventMonitorigMainScreen(suivi: _suivi));
  }

  void _launchEventStatisticsMainScreen() {
    // je redemande les champs pour éviter un problème à l'affichage si on a quitté après avoir vu  une courbe et que l'on revient...
    // car le state vaut StateStatisticsLoadingCharts et cet état n'est pas géré ici
    _blocStatistics.add(EventStatisticsMainScreen());
  }
}
