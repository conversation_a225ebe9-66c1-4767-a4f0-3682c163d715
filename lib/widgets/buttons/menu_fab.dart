import 'package:expandable_bottom_bar/expandable_bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/notifications/NotificationMailObservable.dart';

class MenuFAB extends StatelessWidget {
  final SvgPicture svgPicture;
  final int realIndex;
  final BottomBarController bottomBarController;

  MenuFAB(
      {required this.svgPicture,
      required this.realIndex,
      required this.bottomBarController});

  @override
  Widget build(BuildContext context) {
    final notificationMail = NotificationMailObservable();

    return ValueListenableBuilder<int>(
        valueListenable: notificationMail.notifMailNotifier,
        builder: (context, value, child) {
          return GestureDetector(
              // Set onVerticalDrag event to drag handlers of controller for swipe effect
              onVerticalDragUpdate: bottomBarController.onDrag,
              onVerticalDragEnd: bottomBarController.onDragEnd,
              child: SizedBox(
                height: 60,
                width: 60,
                child: FloatingActionButton(
                  backgroundColor: AppTheme.backgroundColor,
                  child: Stack(
                    children: [
                      Container(
                          padding: EdgeInsets.all(3.0),
                          height: 36.0,
                          width: 36.0,
                          decoration: BoxDecoration(
                              color: realIndex > 1
                                  ? AppTheme.primaryColor
                                  : AppTheme.backgroundColor,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10.0))),
                          child: svgPicture),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                            padding: EdgeInsets.all(1.0),
                            decoration: BoxDecoration(
                                color: value > 0
                                    ? AppTheme.secondaryColor
                                    : Colors.transparent,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                      color: value > 0
                                          ? AppTheme.primaryBlurColor
                                              .withOpacity(0.5)
                                          : Colors.transparent,
                                      spreadRadius: 0,
                                      blurRadius: 10,
                                      offset: Offset(0, 0))
                                ]),
                            constraints: BoxConstraints(
                              minWidth: 12,
                              minHeight: 12,
                            )),
                      )
                    ],
                  ),
                  elevation: 2,
                  onPressed: () => bottomBarController.swap(),
                ),
              ));
        });
  }
}
