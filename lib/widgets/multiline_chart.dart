import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/extensions/int_extension.dart';
import 'package:theradom/extensions/datetime_extension.dart';

/// Widget pour paraméter l'aperçu d'une courbe
/// Migrated from mp_chart to fl_chart

class Multiline<PERSON>hart extends StatefulWidget {
  final List<LineChartBarData>? lineChartBarDataList;
  final List<HorizontalLine?>? limitLineList;
  final bool enableGestures;
  // For backward compatibility with mp_chart
  final List<dynamic>? iLineDataSetList;
  final List<dynamic>? limitLineList2;

  MultilineChart({
    this.lineChartBarDataList,
    this.limitLineList,
    required this.enableGestures,
    this.iLineDataSetList,
    this.limitLineList2,
  });

  @override
  State<StatefulWidget> createState() => _MultilineChartState();
}

class _MultilineChartState extends State<MultilineChart> {
  int _selectedTimestamp = -1;
  List<double> _selectedMeasureList = [];
  double _fontSize = 16.0;
  List<Color> _colorList = AppTheme.chartColorList;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: LineChart(
        _createLineChartData(),
        duration: const Duration(milliseconds: 250),
      ),
    );
  }

  LineChartData _createLineChartData() {
    // Convert from mp_chart format to fl_chart format if needed
    final List<LineChartBarData> barDataList = widget.lineChartBarDataList ??
        _convertMpChartToFlChart(widget.iLineDataSetList);

    final List<HorizontalLine> horizontalLines = widget.limitLineList != null
        ? widget.limitLineList!.whereType<HorizontalLine>().toList()
        : _convertLimitLinesToHorizontalLines(widget.limitLineList2);

    return LineChartData(
      lineTouchData: LineTouchData(
        enabled: widget.enableGestures,
        touchTooltipData: LineTouchTooltipData(
          getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
            return touchedBarSpots.map((barSpot) {
              final timestamp = barSpot.x.toInt();
              final value = barSpot.y;

              return LineTooltipItem(
                '${timestamp.timestamp2LanguageString()}\n${value.toStringAsFixed(1)}',
                TextStyle(
                  color: AppTheme.backgroundColor,
                  fontWeight: FontWeight.bold,
                  fontSize: _fontSize,
                ),
              );
            }).toList();
          },
        ),
        touchCallback: (FlTouchEvent event, LineTouchResponse? touchResponse) {
          if (!widget.enableGestures ||
              touchResponse == null ||
              touchResponse.lineBarSpots == null) {
            return;
          }

          if (event is FlTapUpEvent) {
            final spot = touchResponse.lineBarSpots![0];
            setState(() {
              _selectedTimestamp = spot.x.toInt();
              _selectedMeasureList =
                  touchResponse.lineBarSpots!.map((spot) => spot.y).toList();
            });
          }
        },
      ),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: 1,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: AppTheme.primaryColor.withOpacity(0.3),
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: AppTheme.primaryColor.withOpacity(0.3),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: (value, meta) {
              final timestamp = value.toInt();
              final dateTime = timestamp.timestamp2DateTime();
              final dateStr = dateTime.toLanguageStringShort();

              return Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  dateStr,
                  style: TextStyle(
                    color: AppTheme.primaryColorDarker,
                    fontWeight: FontWeight.bold,
                    fontSize: _fontSize,
                  ),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 1,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toStringAsFixed(1),
                style: TextStyle(
                  color: AppTheme.primaryColorDarker,
                  fontWeight: FontWeight.bold,
                  fontSize: _fontSize,
                ),
              );
            },
            reservedSize: 42,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
      ),
      minX: barDataList.isNotEmpty && barDataList[0].spots.isNotEmpty
          ? barDataList[0].spots.first.x
          : 0,
      maxX: barDataList.isNotEmpty && barDataList[0].spots.isNotEmpty
          ? barDataList[0].spots.last.x
          : 0,
      minY: _getMinY(barDataList),
      maxY: _getMaxY(barDataList),
      lineBarsData: barDataList,
      extraLinesData: ExtraLinesData(
        horizontalLines: horizontalLines,
      ),
    );
  }

  double _getMinY(List<LineChartBarData> barDataList) {
    if (barDataList.isEmpty) return 0;

    double minY = double.infinity;
    for (var lineData in barDataList) {
      for (var spot in lineData.spots) {
        if (spot.y < minY) {
          minY = spot.y;
        }
      }
    }

    // Add some padding below the minimum value
    return minY == double.infinity ? 0 : minY * 0.9;
  }

  double _getMaxY(List<LineChartBarData> barDataList) {
    if (barDataList.isEmpty) return 10;

    double maxY = double.negativeInfinity;
    for (var lineData in barDataList) {
      for (var spot in lineData.spots) {
        if (spot.y > maxY) {
          maxY = spot.y;
        }
      }
    }

    // Add some padding above the maximum value
    return maxY == double.negativeInfinity ? 10 : maxY * 1.1;
  }

  // Convert mp_chart ILineDataSet to fl_chart LineChartBarData
  List<LineChartBarData> _convertMpChartToFlChart(
      List<dynamic>? mpChartDataSets) {
    if (mpChartDataSets == null || mpChartDataSets.isEmpty) {
      return [];
    }

    List<LineChartBarData> result = [];

    for (int i = 0; i < mpChartDataSets.length; i++) {
      final dataSet = mpChartDataSets[i];
      final color = AppTheme.chartColorList[i % AppTheme.chartColorList.length];

      // Extract entries from mp_chart format
      List<FlSpot> spots = [];
      try {
        // Try to extract entries from mp_chart format
        final entries = dataSet.getValues();
        if (entries != null) {
          for (var entry in entries) {
            spots.add(FlSpot(entry.x.toDouble(), entry.y.toDouble()));
          }
        }
      } catch (e) {
        // If extraction fails, use empty list
        print("Error converting mp_chart data: $e");
      }

      // Create LineChartBarData
      result.add(
        LineChartBarData(
          spots: spots,
          isCurved: false,
          color: color,
          barWidth: 2,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
            getDotPainter: (spot, percent, barData, index) {
              return FlDotCirclePainter(
                radius: 3,
                color: color,
                strokeWidth: 1,
                strokeColor: Colors.white,
              );
            },
          ),
          belowBarData: BarAreaData(show: false),
        ),
      );
    }

    return result;
  }

  // Convert mp_chart LimitLine to fl_chart HorizontalLine
  List<HorizontalLine> _convertLimitLinesToHorizontalLines(
      List<dynamic>? limitLines) {
    if (limitLines == null || limitLines.isEmpty) {
      return [];
    }

    List<HorizontalLine> result = [];

    for (var limitLine in limitLines) {
      if (limitLine == null) continue;

      try {
        final y = limitLine.limit;
        final label = limitLine.label;

        result.add(
          HorizontalLine(
            y: y,
            color: AppTheme.chartLimitColor,
            strokeWidth: 1,
            dashArray: [5, 5],
            label: HorizontalLineLabel(
              show: true,
              labelResolver: (line) => label ?? '',
              style: TextStyle(
                color: AppTheme.chartLimitColor,
                fontWeight: FontWeight.bold,
                fontSize: _fontSize,
              ),
              alignment: Alignment.topRight,
            ),
          ),
        );
      } catch (e) {
        print("Error converting limit line: $e");
      }
    }

    return result;
  }

  List<Widget> _getLegendItems() {
    List<Widget> legendItems = [];

    final barDataList = widget.lineChartBarDataList ??
        _convertMpChartToFlChart(widget.iLineDataSetList);

    for (int i = 0; i < barDataList.length; i++) {
      final lineData = barDataList[i];
      final color = _colorList[i % _colorList.length];
      String label =
          lineData.spots.isNotEmpty ? lineData.spots.first.y.toString() : "";

      if (_selectedTimestamp != -1 && i < _selectedMeasureList.length) {
        label = "$label: ${_selectedMeasureList[i]}";
      }

      legendItems.add(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: _fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    }

    return legendItems;
  }
}
