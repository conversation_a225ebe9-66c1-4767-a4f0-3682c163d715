import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/extensions/int_extension.dart';
import 'package:theradom/extensions/datetime_extension.dart';

/// Widget pour paraméter l'aperçu d'une courbe
/// Migrated from mp_chart to fl_chart

class MultilineChart extends StatefulWidget {
  final dynamic
      iLineDataSetList; // Can be List<LineChartBarData> or List<dynamic>
  final dynamic limitLineList2; // Can be List<HorizontalLine> or List<dynamic>
  final bool enableGestures;
  // Legacy support
  final dynamic limitLineList;

  const MultilineChart({
    Key? key,
    required this.iLineDataSetList,
    this.limitLineList2,
    this.limitLineList,
    required this.enableGestures,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _MultilineChartState();
}

class _MultilineChartState extends State<MultilineChart> {
  double _fontSize = 16.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: LineChart(
        _createLineChartData(),
        duration: const Duration(milliseconds: 250),
      ),
    );
  }

  LineChartData _createLineChartData() {
    // Convert data to fl_chart format if needed
    final List<LineChartBarData> barDataList =
        _ensureLineChartBarDataList(widget.iLineDataSetList);
    final List<HorizontalLine> horizontalLines = _ensureHorizontalLineList(
        widget.limitLineList2 ?? widget.limitLineList ?? []);

    return LineChartData(
      lineTouchData: LineTouchData(
        enabled: widget.enableGestures,
        touchTooltipData: LineTouchTooltipData(
          getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
            return touchedBarSpots.map((barSpot) {
              final timestamp = barSpot.x.toInt();
              final value = barSpot.y;

              return LineTooltipItem(
                '${timestamp.timestamp2LanguageString()}\n${value.toStringAsFixed(1)}',
                TextStyle(
                  color: AppTheme.backgroundColor,
                  fontWeight: FontWeight.bold,
                  fontSize: _fontSize,
                ),
              );
            }).toList();
          },
        ),
      ),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: 1,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: AppTheme.primaryColor.withOpacity(0.3),
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: AppTheme.primaryColor.withOpacity(0.3),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: (value, meta) {
              final timestamp = value.toInt();
              final dateTime = timestamp.timestamp2DateTime();
              final dateStr = dateTime.toLanguageStringShort();

              return Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  dateStr,
                  style: TextStyle(
                    color: AppTheme.primaryColorDarker,
                    fontWeight: FontWeight.bold,
                    fontSize: _fontSize,
                  ),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 1,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toStringAsFixed(1),
                style: TextStyle(
                  color: AppTheme.primaryColorDarker,
                  fontWeight: FontWeight.bold,
                  fontSize: _fontSize,
                ),
              );
            },
            reservedSize: 42,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
      ),
      minX: barDataList.isNotEmpty && barDataList[0].spots.isNotEmpty
          ? barDataList[0].spots.first.x
          : 0,
      maxX: barDataList.isNotEmpty && barDataList[0].spots.isNotEmpty
          ? barDataList[0].spots.last.x
          : 0,
      minY: _getMinY(barDataList),
      maxY: _getMaxY(barDataList),
      lineBarsData: barDataList,
      extraLinesData: ExtraLinesData(
        horizontalLines: horizontalLines,
      ),
    );
  }

  double _getMinY(List<LineChartBarData> barDataList) {
    if (barDataList.isEmpty) return 0;

    double minY = double.infinity;
    for (var lineData in barDataList) {
      for (var spot in lineData.spots) {
        if (spot.y < minY) {
          minY = spot.y;
        }
      }
    }

    // Add some padding below the minimum value
    return minY == double.infinity ? 0 : minY * 0.9;
  }

  double _getMaxY(List<LineChartBarData> barDataList) {
    if (barDataList.isEmpty) return 10;

    double maxY = double.negativeInfinity;
    for (var lineData in barDataList) {
      for (var spot in lineData.spots) {
        if (spot.y > maxY) {
          maxY = spot.y;
        }
      }
    }

    // Add some padding above the maximum value
    return maxY == double.negativeInfinity ? 10 : maxY * 1.1;
  }

  // Ensure we have a List<LineChartBarData>
  List<LineChartBarData> _ensureLineChartBarDataList(dynamic data) {
    if (data == null) return [];

    if (data is List<LineChartBarData>) {
      return data;
    }

    // If it's a List<dynamic>, check each element
    if (data is List) {
      List<LineChartBarData> result = [];
      for (var item in data) {
        if (item is LineChartBarData) {
          result.add(item);
        } else {
          print(
              "🔍 CHART DEBUG: Skipping non-LineChartBarData item: ${item.runtimeType}");
        }
      }
      return result;
    }

    return [];
  }

  // Ensure we have a List<HorizontalLine>
  List<HorizontalLine> _ensureHorizontalLineList(dynamic data) {
    if (data == null) return [];

    if (data is List<HorizontalLine>) {
      return data;
    }

    // If it's a List<dynamic>, try to convert or return empty
    if (data is List) {
      try {
        return data.cast<HorizontalLine>();
      } catch (e) {
        print("Could not convert limit lines: $e");
        return [];
      }
    }

    return [];
  }
}
