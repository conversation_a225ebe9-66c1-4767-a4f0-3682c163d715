import 'package:bloc/bloc.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/blocs/login/EventLogin.dart';
import 'package:theradom/blocs/login/StateLogin.dart';
import 'package:theradom/config.dart';
import 'package:theradom/constants/error_codes.dart';

import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/utils/Helper.dart';

import 'package:theradom/utils/Translations.dart';
import 'package:theradom/models/BasicUserInfos.dart';
import 'package:theradom/models/Credentials.dart';

import 'package:theradom/services/repositories/mobile_authentication_repository.dart';
import 'package:theradom/services/repositories/send_email_repository.dart';
import 'package:theradom/utils/EncryptionUtils.dart';
import 'package:theradom/utils/firebasenotifications/FirebaseNotificationService.dart';

import 'package:theradom/services/repositories/mobile_password_reset_repository.dart';
import 'package:theradom/utils/helper_message.dart';

class BlocLogin extends Bloc<EventLogin, StateLogin> {
  final BlocAuthentication blocAuthentification;
  late final MobileAuthenticationRepository _mobileAuthentificationRepository;

  BlocLogin(
      {required this.blocAuthentification,
      MobileAuthenticationRepository? mobileAuthentificationRepository,
      SendEmailRepository? sendEmailRepository})
      : super(StateLoginInitial()) {
    _mobileAuthentificationRepository =
        mobileAuthentificationRepository ?? MobileAuthenticationRepository();
    on<EventLogginBtnPressed>(_onLogInBtnPressed);
    on<EventLoginForgotPassword>(_onPasswordForgotten);
    on<EventLoginNewDevice>(_sendOTP);
  }

  void _onLogInBtnPressed(
      EventLogginBtnPressed event, Emitter<StateLogin> emit) async {
    // je check la validité du login/password et j'appelle le service de login

    emit(StateLoginLoading(loading: true));

    if ((event.user == "") || (event.password == "")) {
      emit(StateLoginLoading(loading: false));
      emit(StateLoginFailure(
          titleError: allTranslations.text('error'),
          messageError: allTranslations.text("txt_enterCredentials")));
    } else {
      String pwdHash = event.biometricAuth
          ? event.password
          : EncryptionUtils(event.password).toMd5();
      String appToken = await FirebaseNotificationService.instance
          .getDeviceToken(); // ligne à modifier pour changer appToken
      try {
        Credentials credentials = Credentials(
            login: event.user, password: pwdHash, appToken: appToken);
        String credentialsJson = credentialsToJson(credentials);
        var result = await _mobileAuthentificationRepository
            .authenticate(credentialsJson);
        BasicUserInfos basicUserInfos = result['basicUserInfos'];

        //print("test de basicuserinfos: " + Config.basicUserInfos!.numpat);

        bool otpRequired = result['otpRequired'];
        bool otpForced = result['otpForced'];

//
        Config.userAccountInfos!.INS = basicUserInfos.ins;
        blocAuthentification.add(EventLoggedIn(
            basicUserInfos: basicUserInfos,
            login: event.user,
            password: pwdHash,
            newAppId: otpRequired,
            MFA: otpForced));
        emit(StateLoginLoading(loading: false));
        //emit(StateLoginInitial());

        emit(StateLoginInitial());
      } on WebServiceCallException catch (e) {
        emit(StateLoginLoading(loading: false));
        emit(StateLoginFailure(
            titleError: "${allTranslations.text('error')}",
            messageError: e.toString()));
      }
    }
  }

  void _sendOTP(EventLoginNewDevice event, Emitter<StateLogin> emit) async {
    //faut trouver un moyen de transmettre le bon login, eventuellement en le récupérant de l'event
    //emit(StateLoginLoading(loading: true));
    MobileResetPasswordRepository passwordResetRepo =
        MobileResetPasswordRepository();

    String ins = event.ins;
    String login = Config.userAccountInfos!.login;
    String email = Config.basicUserInfos!.mail;

    //String ins = Config.basicUserInfos!.ins;

    var result = await passwordResetRepo.sendResetPassword(ins, login, email);
    //emit(StateLoginLoading(loading: false));
    if (result["valide"] != 'OK') {
      emit(StateLoginFailure(
          titleError: "${allTranslations.text('error')}",
          messageError: result.message));
    } else {}
  }

  void _onPasswordForgotten(
      EventLoginForgotPassword event, Emitter<StateLogin> emit) async {
    //emit(StateLoginLoading(loading: false)); // Émettre un état de chargement

    MobileResetPasswordRepository passwordResetRepo =
        MobileResetPasswordRepository();
    String ins = Config.userAccountInfos!.INS;
    String login = Config.userAccountInfos!.login;
    String email = event.email;
    var result = await passwordResetRepo.sendResetPassword(ins, login, email);
    //emit(StateLoginLoading(loading: false));

    // Vérifier si le résultat est un Map et contient la clé 'valide' avec la valeur 'OK'
    // Si la demande de réinitialisation est validée
    if (result.valide == 'OK') {
      // Émet un état de succès avec le message de confirmation
      emit(StateLoginModifyPassword(
          success: true,
          title: allTranslations.text("txt_validEmail"),
          message: "${allTranslations.text("txt_messageSentTo")} $email"));
      // Confirme l'envoi de l'email
      emit(StateLoginEmailSent(success: true));
    } else {
      // En cas d'erreur, extrait le code d'erreur du message
      // Le message est au format "code|description"
      String errorId = HelperMessage.getCodeFromMessage(result.erreur);

      // Émet différents états selon le code d'erreur reçu
      switch (errorId) {
        case ErrorCodes.EMAIL_UNKNOWN:
          // L'email fourni n'existe pas dans la base de données
          emit(StateLoginEmailUnknown(
              titleError: "${allTranslations.text('txt_invalidEmailTitle')}",
              messageError:
                  allTranslations.text(ErrorCodes.getFullErrorCode(errorId))));
          break;
        case ErrorCodes.ACCOUNT_DISABLED:
          // Le compte utilisateur est désactivé
          emit(StateLoginAccountBlocked(
              titleError: "${allTranslations.text('txt_invalidEmailTitle')}",
              messageError:
                  allTranslations.text(ErrorCodes.getFullErrorCode(errorId))));
          break;
        default:
          // Gestion des autres types d'erreurs
          emit(StateLoginFailure(
              titleError: "${allTranslations.text('txt_invalidEmailTitle')}",
              messageError:
                  allTranslations.text(ErrorCodes.getFullErrorCode(errorId))));
      }
    }
  }
}
