name: theradom
description: Application Theradom pour Android et iOS
publish_to: none

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html

version: 3.0.9

environment:
  sdk: '>=3.1.0 <4.0.0'

dependencies:
  bloc: ^9.0.0
  flutter_bloc: ^9.1.1
  http: ^1.1.2
  another_flushbar: ^1.12.30
  qr_code_scanner: ^1.0.1
  encrypt: ^5.0.3
  settings_ui: ^2.0.2
  date_format: ^2.0.7
  auto_size_text: ^3.0.0
  flutter_slidable: ^4.0.0
  path_provider: ^2.1.1
  open_filex: ^4.3.4
  permission_handler: ^12.0.0+1
  local_auth: ^2.1.7
  local_auth_android: ^1.0.36
  record: ^6.0.0
  audioplayers: ^6.4.0
  
  table_calendar: ^3.0.9
  add_2_calendar: ^3.0.1
  flutter_secure_storage: ^9.0.0
  sticky_headers: ^0.3.0+2
  share_plus: ^11.0.0
  mask_text_input_formatter: ^2.7.0
  file_picker: ^10.2.0
  pdf: ^3.10.7
  wechat_assets_picker: ^9.0.0-dev.2
  firebase_messaging: ^15.2.7
  firebase_core: ^3.14.0
  fl_chart: ^1.0.0
  # Commentez temporairement mp_chart
  # mp_chart:
  #   path: lib/utils/MPFlutterChart/mp_chart/
  font_awesome_flutter: ^10.6.0
  modal_bottom_sheet: ^3.0.0-pre # ne pas mettre à jour avant une vraie 3.0.0
  introduction_screen: ^3.1.12
  # slider_button: ^2.0.0 # Temporarily commented out due to compatibility issues
  expandable_bottom_bar: ^2.0.2
  # vibration: ^1.9.0 # Temporarily commented out due to compatibility issues
  html_unescape: ^2.0.0
  device_info_plus: ^10.1.0
  shimmer: ^3.0.0
  flutter_svg: ^2.0.9
  rxdart: ^0.28.0
  flutter_localization: ^0.3.2
  envied: ^1.1.1

  flutter:
    sdk: flutter

  url_launcher: ^6.2.2
  url_launcher_ios: ^6.2.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

dev_dependencies:
  flutter_native_splash: ^2.3.8
  envied_generator: ^1.1.1
  build_runner: ^2.4.7

  flutter_test:
    sdk: flutter

flutter_native_splash:
  color: "ffffff"
  image: "assets/images/logo/pastille_400.png"

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/Syncopate-Regular.ttf
    - assets/fonts/Kanit-Light.ttf
    - assets/fonts/OpenSans-Regular.ttf
    - assets/fonts/OpenSans-SemiBold.ttf
    - assets/fonts/OpenSans-Bold.ttf
    - assets/fonts/CustomIconData.ttf
    
    - assets/pdf/cgu_v3.pdf
    - assets/pdf/manuel_theradom.pdf

    - assets/images/icons/icShow.svg
    - assets/images/icons/icNoShow.svg
    - assets/images/icons/icInfoApp.svg
    - assets/images/icons/icQRcode.svg

    - assets/i18n/fr.json
    - assets/i18n/en.json
    - assets/i18n/it.json

    - assets/flags/fr.svg
    - assets/flags/it.svg
    - assets/flags/en.svg

    - assets/images/smileys/smiley_pain_0.svg
    - assets/images/smileys/smiley_pain_1.svg
    - assets/images/smileys/smiley_pain_2.svg
    - assets/images/smileys/smiley_pain_3.svg
    - assets/images/smileys/smiley_pain_4.svg
    - assets/images/smileys/smiley_pain_5.svg

    - assets/images/logo/logo_ce.png
    - assets/images/logo/logo_usine.jpg
    - assets/images/logo/logo_ifu.png
    - assets/images/icons/icPrescrip.svg
    - assets/images/icons/icStat.svg
    - assets/images/icons/icNewMail.svg
    - assets/images/icons/btChevBleuL.svg
    - assets/images/icons/btChevBleuR.svg
    - assets/images/icons/btChevDw.svg
    - assets/images/icons/icPaperclip.svg
    - assets/images/icons/icSendMessage.svg
    - assets/images/icons/icSuivi.svg
    - assets/images/icons/icMenu.svg
    - assets/images/icons/icCalendar.svg
    - assets/images/icons/icSettings.svg
    - assets/images/icons/icBio.svg
    - assets/images/icons/icMessagerie.svg
    - assets/images/icons/icFile.svg
    - assets/images/icons/icFileEmpty.svg
    - assets/images/icons/icProfile.svg
    - assets/images/icons/icCross.svg
    - assets/images/icons/btChevBlancL.svg
    - assets/images/icons/icLock.svg
    - assets/images/icons/btChevBlancR.svg
    - assets/images/icons/icLogOut.svg
    - assets/images/icons/icTextSize.svg
    - assets/images/icons/icUserGuide.svg
    - assets/images/icons/icUserTimer.svg
    - assets/images/icons/icDocCgu.svg
    - assets/images/icons/icPrint.svg
    - assets/images/icons/icTrash.svg
    - assets/images/icons/icNewSeance.svg
    - assets/images/icons/icClock.svg
    - assets/images/icons/btChevBlancDw.svg
    - assets/images/icons/btChevBlancUp.svg
    - assets/images/icons/icSeanceLocked.svg
    - assets/images/icons/icSeanceUnlocked.svg
    - assets/images/icons/icShare.svg
    - assets/images/icons/icMailReply.svg
    - assets/images/icons/icClear.svg
    - assets/images/icons/icToTime.svg
    - assets/images/icons/icFullScreen.svg
    - assets/images/icons/icCgv.svg
    - assets/images/icons/icSaveCoulBig.svg
    - assets/images/icons/icEdit.svg
    - assets/images/icons/icAddFile.svg
    - assets/images/icons/icReduceScreen.svg
    - assets/images/icons/icBack.svg
    - assets/images/icons/icFilePdf.svg
    - assets/images/icons/icNext.svg
    - assets/images/icons/icSuiviBis.svg
    - assets/images/icons/icPrivacyPolicy.svg
    - assets/images/icons/smartphone.svg

    - assets/images/logo/theradom.png
    - assets/images/logo/pastille_400.png

    - assets/images/onboarding/theradom_uni.png
    - assets/images/onboarding/imgOnboarding1.png
    - assets/images/onboarding/imgOnboarding2.png
    - assets/images/onboarding/imgOnboarding3.png
    - assets/images/onboarding/imgOnboarding4.png
    - assets/images/onboarding/imgOnboarding5.png
    - assets/images/first_launch/imgScan.png









  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

  fonts:
    - family: Syncopate
      fonts:
        - asset: assets/fonts/Syncopate-Regular.ttf
    - family: Kanit
      fonts:
        - asset: assets/fonts/Kanit-Light.ttf
    - family: OpenSans
      fonts:
        - asset: assets/fonts/OpenSans-Regular.ttf
        - asset: assets/fonts/OpenSans-SemiBold.ttf
        - asset: assets/fonts/OpenSans-Bold.ttf
    - family: CustomIconData
      fonts:
        - asset: assets/fonts/CustomIconData.ttf
